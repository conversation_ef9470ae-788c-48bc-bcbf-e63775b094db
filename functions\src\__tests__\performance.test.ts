import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import { generateEmpruntLabels } from "../emprunts/generateEmpruntLabels";
import { createEmprunt } from "../emprunts/createEmprunt";
import { updateEmpruntStatus } from "../emprunts/updateEmpruntStatus";

// Mock Firebase Admin
jest.mock("firebase-admin", () => ({
  firestore: jest.fn(() => ({
    collection: jest.fn(),
    getAll: jest.fn(),
    runTransaction: jest.fn(),
  })),
  FieldValue: {
    serverTimestamp: jest.fn(() => "TIMESTAMP"),
  },
  Timestamp: {
    fromDate: jest.fn((date) => ({ toDate: () => date })),
  },
}));

// Mock Firebase Functions
jest.mock("firebase-functions/v1", () => ({
  https: {
    HttpsError: class HttpsError extends Error {
      constructor(public code: string, public message: string) {
        super(message);
        this.name = "HttpsError";
      }
    },
    onCall: jest.fn((handler) => handler),
  },
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock pdf-lib pour la performance
jest.mock("pdf-lib", () => ({
  PDFDocument: {
    create: jest.fn(() => ({
      addPage: jest.fn(() => ({
        drawText: jest.fn(),
        drawRectangle: jest.fn(),
        getSize: jest.fn(() => ({ width: 595, height: 842 })),
      })),
      embedFont: jest.fn(),
      save: jest.fn(() => Promise.resolve(new Uint8Array([1, 2, 3]))),
    })),
  },
  rgb: jest.fn(),
  StandardFonts: {
    Helvetica: "Helvetica",
    HelveticaBold: "HelveticaBold",
  },
}));

// Mock auth utility
jest.mock("../utils/auth", () => ({
  checkRegisseurOrAdmin: jest.fn(),
}));

// Mock validation utility
jest.mock("../utils/validation", () => ({
  validateEmpruntData: jest.fn(),
}));

const mockDb = {
  collection: jest.fn(),
  getAll: jest.fn(),
  runTransaction: jest.fn(),
};

const mockCheckRegisseurOrAdmin = require("../utils/auth").checkRegisseurOrAdmin;
const mockValidateEmpruntData = require("../utils/validation").validateEmpruntData;

describe("Performance Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (admin.firestore as unknown as jest.Mock).mockReturnValue(mockDb);
  });

  describe("PDF Generation Performance", () => {
    it("should generate PDF in less than 3 seconds using real logic", async () => {
      // Arrange - Configuration pour un test de performance réaliste
      const mockRequest = {
        data: { empruntId: "test-emprunt-id" },
        auth: { uid: "test-user-id" },
      };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({
          nom: "Test Manipulation Performance",
          lieu: "Salle de test",
          dateDepart: { toDate: () => new Date("2024-12-01") },
          dateRetourPrevue: { toDate: () => new Date("2024-12-05") },
          referent: "Test Référent",
          emprunteur: "Test Emprunteur",
        }),
      };

      // Simuler plusieurs modules pour tester la performance
      const mockMaterielSnapshot = {
        docs: Array.from({ length: 10 }, (_, i) => ({
          data: () => ({
            type: "module",
            idMateriel: `module${i}`,
            quantite: 1,
          }),
        })),
      };

      const mockModuleDocs = Array.from({ length: 10 }, (_, i) => ({
        exists: true,
        data: () => ({ nom: `Module Test ${i}` }),
      }));

      const mockEmpruntRef = {
        get: jest.fn().mockResolvedValue(mockEmpruntDoc),
        collection: jest.fn(() => ({
          get: jest.fn().mockResolvedValue(mockMaterielSnapshot),
        })),
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });
      mockDb.getAll.mockResolvedValue(mockModuleDocs);

      // Act - Mesurer la performance réelle
      const startTime = Date.now();
      const result = await generateEmpruntLabels(mockData, mockContext);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Assert
      expect(result.success).toBe(true);
      expect(duration).toBeLessThan(3000); // Contrainte de 3 secondes
      expect(mockCheckRegisseurOrAdmin).toHaveBeenCalledWith(mockContext);
    });

    it("should handle large datasets efficiently", async () => {
      // Arrange - Test avec un grand nombre de modules
      const mockData = { empruntId: "test-emprunt-large" };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({
          nom: "Test Large Dataset",
          lieu: "Salle de test",
          dateDepart: { toDate: () => new Date("2024-12-01") },
          dateRetourPrevue: { toDate: () => new Date("2024-12-05") },
          referent: "Test Référent",
          emprunteur: "Test Emprunteur",
        }),
      };

      // Simuler 50 modules pour tester la scalabilité
      const mockMaterielSnapshot = {
        docs: Array.from({ length: 50 }, (_, i) => ({
          data: () => ({
            type: i % 2 === 0 ? "module" : "stock",
            idMateriel: `item${i}`,
            quantite: Math.floor(Math.random() * 10) + 1,
          }),
        })),
      };

      const mockItemDocs = Array.from({ length: 50 }, (_, i) => ({
        exists: true,
        data: () => ({ nom: `Item Test ${i}` }),
      }));

      const mockEmpruntRef = {
        get: jest.fn().mockResolvedValue(mockEmpruntDoc),
        collection: jest.fn(() => ({
          get: jest.fn().mockResolvedValue(mockMaterielSnapshot),
        })),
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });
      mockDb.getAll
        .mockResolvedValueOnce(mockItemDocs.slice(0, 25)) // Modules
        .mockResolvedValueOnce(mockItemDocs.slice(25, 50)); // Stocks

      // Act
      const startTime = Date.now();
      const result = await generateEmpruntLabels(mockData, mockContext);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Assert
      expect(result.success).toBe(true);
      expect(duration).toBeLessThan(5000); // Contrainte élargie pour gros dataset
      expect(mockDb.getAll).toHaveBeenCalledTimes(2); // Optimisation N+1 vérifiée
    });
  });

  describe("Cloud Functions Performance", () => {
    it("should create emprunt in reasonable time using real logic", async () => {
      // Arrange
      const mockData = {
        nom: "Test Performance",
        lieu: "Salle de test",
        dateDepart: new Date("2024-12-01"),
        dateRetourPrevue: new Date("2024-12-05"),
        secteur: "Test",
        referent: "Test",
        emprunteur: "Test",
        materiel: [
          { type: "module", idMateriel: "module1", quantite: 1 },
        ],
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      mockValidateEmpruntData.mockReturnValue(mockData);
      const mockEmpruntRef = { id: "test-id", collection: jest.fn() };
      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });
      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback({ set: jest.fn() });
      });

      // Act
      const startTime = Date.now();
      const result = await createEmprunt(mockData, mockContext);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Assert
      expect(result.success).toBe(true);
      expect(duration).toBeLessThan(1000); // 1 seconde max
    });

    it("should update status in reasonable time using real logic", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Prêt",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Pas prêt" }),
      };

      const mockTransaction = {
        get: jest.fn()
          .mockResolvedValueOnce(mockEmpruntDoc)
          .mockResolvedValueOnce({ docs: [] }),
        update: jest.fn(),
        set: jest.fn(),
        getAll: jest.fn().mockResolvedValue([]),
      };

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act
      const startTime = Date.now();
      const result = await updateEmpruntStatus(mockData, mockContext);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Assert
      expect(result.success).toBe(true);
      expect(duration).toBeLessThan(500); // 500ms max
    });

    it("should handle concurrent operations efficiently", async () => {
      // Arrange - Test de performance avec opérations concurrentes
      const mockData = {
        empruntId: "test-concurrent",
        newStatus: "Prêt",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Pas prêt" }),
      };

      const mockTransaction = {
        get: jest.fn()
          .mockResolvedValue(mockEmpruntDoc)
          .mockResolvedValueOnce({ docs: [] }),
        update: jest.fn(),
        set: jest.fn(),
        getAll: jest.fn().mockResolvedValue([]),
      };

      mockDb.runTransaction.mockImplementation(async (callback) => {
        // Simuler une latence réseau
        await new Promise(resolve => setTimeout(resolve, 10));
        return await callback(mockTransaction);
      });

      // Act - Exécuter plusieurs opérations en parallèle
      const startTime = Date.now();
      const promises = Array.from({ length: 5 }, () =>
        updateEmpruntStatus(mockData, mockContext)
      );
      const results = await Promise.all(promises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Assert
      expect(results).toHaveLength(5);
      results.forEach(result => expect(result.success).toBe(true));
      expect(duration).toBeLessThan(1000); // Toutes les opérations en moins d'1 seconde
    });
  });
});
