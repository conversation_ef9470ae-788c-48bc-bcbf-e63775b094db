# Briefing de Mission FINALE (v2) : E-2-Tests-Application-Stricte-du-Template

## 1. OBJECTIF STRATÉGIQUE
Corriger TOUTES les erreurs de test en appliquant de manière stricte et systématique le template de test fourni pour `createEmprunt.test.ts` à l'ensemble des fichiers de test.

## 2. CONTEXTE
- Ton application du template a échoué. Les erreurs montrent que tu as oublié deux règles fondamentales :
    1.  Tu n'as PAS utilisé la fonction `testEnv.wrap()` pour appeler les fonctions Cloud dans les tests.
    2.  Tu n'as PAS appliqué correctement le mocking de `firebase-admin` partout.
- Le seul modèle de code que tu as le droit de suivre est le fichier `createEmprunt.test.ts` que je t'ai fourni précédemment.

## 3. CRITÈRE DE SUCCÈS UNIQUE ET NON-NÉGOCIABLE
- [ ] La commande `npm run test:ci` dans le dossier `functions` se termine avec **0 échecs**.

## 4. RÈGLES D'ACTION IMPÉRATIVES
1.  **Commande de Test :** Tu dois OBLIGATOIREMENT utiliser la commande `npm run test:ci` pour valider ton travail, et non `npm test`.
2.  **Structure des Fichiers de Test :** Pour CHAQUE fichier de test (`auth.test.ts`, `updateEmpruntStatus.test.ts`, etc.) :
    * Tu DOIS placer le bloc `jest.mock('firebase-admin', ...)` tout en haut du fichier, avant les `import` de tes fonctions.
    * Tu DOIS initialiser l'environnement avec `const testEnv = ftest(...)`.
    * Tu DOIS nettoyer l'environnement avec `testEnv.cleanup()` dans un `afterAll`.
3.  **Appel des Fonctions :** Pour CHAQUE appel à une Cloud Function dans un test :
    * Tu DOIS d'abord la "wrapper" : `const wrapped = testEnv.wrap(nomDeLaFonction);`.
    * Tu DOIS ensuite l'appeler via cette variable "wrappée" : `await wrapped(data, context);`.
4.  **Finalisation :** Une fois que `npm run test:ci` passe au vert, fais un `git commit --amend` et `git push --force` pour finaliser la PR.

---
**RÉFÉRENCE - LE SEUL MODÈLE VALIDE :**
// 1. Initialisation de l'environnement de test
import * as ftest from 'firebase-functions-test';
import * as admin from 'firebase-admin';

// 2. Mocking de firebase-admin AVANT l'import de la fonction
// Cela intercepte tous les appels à admin.initializeApp(), firestore(), etc.
jest.mock('firebase-admin', () => ({
  initializeApp: jest.fn(),
  firestore: jest.fn(() => ({
    collection: jest.fn().mockReturnThis(),
    doc: jest.fn().mockReturnThis(),
    runTransaction: jest.fn(callback => callback({
      get: jest.fn().mockResolvedValue({ exists: true }),
      set: jest.fn(),
      update: jest.fn(),
    })),
  })),
}));

// 3. Import de la fonction APRÈS le mocking
import { createEmprunt } from '../emprunts/createEmprunt';

// Initialisation de l'environnement de test avec un projet hors ligne
const testEnv = ftest({
  projectId: 'sigma-agent-test',
});

describe('createEmprunt Cloud Function', () => {

  afterAll(() => {
    testEnv.cleanup(); // Nettoyage après les tests
  });

  it('devrait créer un emprunt avec succès avec des données valides', async () => {
    // 4. Wrapper la fonction pour la rendre appelable dans les tests
    const wrapped = testEnv.wrap(createEmprunt);

    const data = {
      nom: 'Test Emprunt',
      lieu: 'Studio A',
      dateDepart: new Date().toISOString(),
      dateRetourPrevue: new Date(Date.now() + 86400000).toISOString(), // Demain
      secteur: 'Production',
      referent: 'John Doe',
      emprunteur: 'Jane Doe',
      materiel: [],
    };

    const context = {
      auth: {
        uid: 'test-uid',
        token: { role: 'regisseur' },
      },
    };

    // 5. Appeler la fonction wrappée
    const result = await wrapped(data, context);

    expect(result).toHaveProperty('id');
    expect(result).toHaveProperty('message', 'Emprunt créé avec succès');
  });

  it('devrait retourner une erreur de permission si l\'utilisateur n\'est pas régisseur ou admin', async () => {
    const wrapped = testEnv.wrap(createEmprunt);
    const data = {};
    const context = {
      auth: {
        uid: 'user-uid',
        token: { role: 'utilisateur' }, // Rôle insuffisant
      },
    };

    // Utiliser `expect.assertions` pour s'assurer que l'erreur est bien levée
    expect.assertions(2);
    try {
      await wrapped(data, context);
    } catch (error: any) {
      expect(error.code).toBe('permission-denied');
      expect(error.message).toBe('Accès refusé. Rôle requis : régisseur ou admin.');
    }
  });

  // ... Ajoutez ici les autres cas de test (données invalides, etc.)
});